// src/index.ts
import { Hono } from "hono";
import * as schema from "@socialfeed/drizzle-schema/d1";

// Importiere alle Typen und Services
import {
  AppContext,
  Bindings,
  PaginationQueueMessage,
  SyncQueueMessage,
  PlatformConnection,
  Post,
} from "./types";

// TikTok Queue Message Types
interface TikTokPollingQueueMessage {
  platformConnectionId: string;
  priority?: number;
}

interface TikTokBackoffQueueMessage {
  platformConnectionId: string;
  delayUntil: number;
}
import {
  getDbClient,
  updateConnectionStatus,
  upsertPost,
  getConnectionDetails,
  getActiveInstagramConnections,
  getActiveTikTokConnections,
  getActiveYouTubeConnections,
  getActiveFacebookConnections,
  getConnectionsForTokenCheck,
} from "./database-service";
import { DebouncerDO } from "./debouncer-do";
import { AccountPollingDO, RateLimiterDO } from "./polling-do";
import { logErrorToAnalytics } from "./analytics-utils";
import { fetchNextCommentPage, GraphApiError } from "./graph-api-shared";
import { getPlatformAdapter } from "./platforms";
import { decryptToken } from "./token-utils";
import { timing } from "hono/timing";
import { secureHeaders } from "hono/secure-headers";
import { desc, eq, and } from "drizzle-orm";

import { DrizzleD1Database } from "drizzle-orm/d1";
import {
  metaWebhookHandler,
  tiktokWebhookHandler,
  youtubeWebhookHandler,
} from "./hono/webhooks";
import refreshTokensHandler from "./cron/refresh-tokens";
import feedPostsHandler from "./hono/feed/posts";
import { propelAuthMiddleware } from "./hono/middleware/root";
import { apiKeyAuthMiddleware } from "./hono/middleware/apiKey";
import { createWebhookVerificationMiddleware } from "./hono/middleware/webhook/verify";
import { youtubeAuthorizeHandler } from "./hono/oauth/youtube/authorize";
import { youtubeCallbackHandler } from "./hono/oauth/youtube/callback";
import { metaAuthorizeHandler } from "./hono/oauth/meta/authorize";
import { metaCallbackHandler } from "./hono/oauth/meta/callback";
import { tiktokAuthorizeHandler } from "./hono/oauth/tiktok/authorize";
import { tiktokCallbackHandler } from "./hono/oauth/tiktok/callback";
import { validatePublicLink } from "./hono/public/link/validate";
import {
  selectFacebookPagePublic,
  selectPageBodyValidator,
} from "./hono/public/link/select-page";
import { mnageHandler } from "./hono/manage";

// Token Bucket implementation for rate limiting
class TokenBucket {
  private tokens: number;
  private lastRefill: number;

  constructor(
    private capacity: number,
    private refillRate: number // in milliseconds
  ) {
    this.tokens = capacity;
    this.lastRefill = Date.now();
  }

  private refill() {
    const now = Date.now();
    const timePassed = now - this.lastRefill;
    const tokensToAdd = Math.floor(timePassed / this.refillRate);

    if (tokensToAdd > 0) {
      this.tokens = Math.min(this.capacity, this.tokens + tokensToAdd);
      this.lastRefill = now;
    }
  }

  consume(tokens: number): boolean {
    this.refill();

    if (this.tokens >= tokens) {
      this.tokens -= tokens;
      return true;
    }

    return false;
  }
}

// --- Typ-Definition für Hono's Umgebung und Kontext ---
// Definiert die Umgebungsvariablen und die Variablen, die wir im Kontext speichern
const app = new Hono<AppContext>()
  // MIDDLEWARE
  .use("*", timing())
  .use("*", secureHeaders()) // Fügt Security Header hinzu
  .use("*", propelAuthMiddleware)
  .use("/feed/:feedId/*", apiKeyAuthMiddleware) // API Key Auth Middleware for /feeds/ endpoints
  .use("/webhooks/meta", createWebhookVerificationMiddleware("meta")) // Webhook Verification Middleware for Meta
  .use("/webhooks/youtube/*", createWebhookVerificationMiddleware("youtube"))
  .use("/webhooks/tiktok/*", createWebhookVerificationMiddleware("tiktok"))
  // ROUTING
  .get("/feed/:feedId/posts", feedPostsHandler)
  .route("/manage", mnageHandler)
  // YouTube OAuth routes
  .get("/oauth/youtube/authorize", youtubeAuthorizeHandler)
  .get("/oauth/youtube/callback", youtubeCallbackHandler)
  // Meta OAuth routes (Instagram & Facebook)
  .get("/oauth/meta/authorize", metaAuthorizeHandler)
  .get("/oauth/meta/callback", metaCallbackHandler)
  // TikTok OAuth routes
  .get("/oauth/tiktok/authorize", tiktokAuthorizeHandler)
  .get("/oauth/tiktok/callback", tiktokCallbackHandler)
  // YouTube webhook route
  .get("/webhooks/youtube", youtubeWebhookHandler)
  .post("/webhooks/youtube", youtubeWebhookHandler)
  // Meta webhook route
  .get("/webhooks/meta", metaWebhookHandler)
  .post("/webhooks/meta", metaWebhookHandler)
  // TikTok webhook route (for app review / future use)
  .get("/webhooks/tiktok", tiktokWebhookHandler)
  .post("/webhooks/tiktok", tiktokWebhookHandler)
  .options("/public/link/:token/validate", (c) => {
    c.header("Access-Control-Allow-Origin", "*");
    c.header("Access-Control-Allow-Methods", "GET, OPTIONS");
    c.header("Access-Control-Allow-Headers", "Content-Type");
    return c.text("OK");
  })
  .get("/public/link/:token/validate", validatePublicLink)
  .options("/public/link/:token/select-page", (c) => {
    c.header("Access-Control-Allow-Origin", "*");
    c.header("Access-Control-Allow-Methods", "POST, OPTIONS");
    c.header("Access-Control-Allow-Headers", "Content-Type");
    return c.text("OK");
  })
  .post(
    "/public/link/:token/select-page",
    selectPageBodyValidator,
    selectFacebookPagePublic
  );

// --- Queue Consumer für Paginierung ---
async function processPaginationQueue(
  batch: MessageBatch<PaginationQueueMessage>,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Processing ${batch.messages.length} pagination messages...`);
  const db = getDbClient(env.DB);
  const promises = batch.messages.map(async (message) => {
    const { platformConnectionId, mediaId, nextPageUrl } = message.body;
    console.log(
      `Processing next page for pcId=${platformConnectionId} mediaId=${mediaId}`
    );
    try {
      const connection = await getConnectionDetails(db, platformConnectionId);
      if (
        !connection ||
        !connection.isActive ||
        !connection.accessTokenEncrypted
      ) {
        console.warn(
          `Queue Pag: Conn ${platformConnectionId} inactive/no token.`
        );
        return;
      }
      const accessToken = await decryptToken(
        connection.accessTokenEncrypted,
        env.ENCRYPTION_KEY
      );
      if (!accessToken) {
        await updateConnectionStatus(db, platformConnectionId, {
          needsReconnect: true,
        });
        logErrorToAnalytics(
          env,
          "QUEUE_PAGINATION_DECRYPT_FAIL",
          `Failed decrypt token`,
          { platformConnectionId, mediaId }
        );
        return;
      }

      // API Call via Service
      const pageResult = await fetchNextCommentPage(nextPageUrl, env);

      if (!pageResult) {
        const currentStatus = await getConnectionDetails(
          db,
          platformConnectionId
        );
        if (!currentStatus?.isActive || currentStatus?.needsReconnect) return; // Kein Retry bei Auth Fehler
        throw new Error(`Failed to fetch next comment page (API Client Error)`);
      }
      const { comments: commentsToWrite, nextPageUrl: subsequentNextPageUrl } =
        pageResult;

      // Skip comment processing - comments are not stored
      console.log(
        `Queue Pag: Skipping ${commentsToWrite.length} comments for media ${mediaId} (comments not stored)`
      );
      // Enqueue next page
      if (subsequentNextPageUrl) {
        const nextMessage: PaginationQueueMessage = {
          platformConnectionId,
          mediaId,
          nextPageUrl: subsequentNextPageUrl,
        };
        await env.PAGINATION_QUEUE.send(nextMessage);
      }
    } catch (error: any) {
      console.error(
        `Queue Pag: Failed processing message for pcId=${platformConnectionId} mediaId=${mediaId}, attempt ${message.attempts}:`,
        error
      );
      const maxRetries = parseInt(env.QUEUE_MAX_RETRIES || "5", 10);
      if (message.attempts >= maxRetries) {
        logErrorToAnalytics(
          env,
          "QUEUE_FINAL_FAILURE",
          `Pagination message failed all retries`,
          {
            platformConnectionId,
            mediaId,
            nextPageUrl,
            attempts: message.attempts,
            error: String(error),
          }
        );
      } else {
        message.retry({ delaySeconds: 30 * (message.attempts + 1) });
      } // Retry mit Backoff
    }
  });
  await Promise.allSettled(promises);
}

// --- Queue Consumer für Post Sync ---
async function processSyncQueue(
  batch: MessageBatch<SyncQueueMessage>,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Processing ${batch.messages.length} sync messages...`);
  const db = getDbClient(env.DB);
  const promises = batch.messages.map(async (message) => {
    const { platformConnectionId, triggeredByUserId } = message.body;
    console.log(
      `Sync Queue: Starting sync for connection ${platformConnectionId}`
    );
    try {
      const connection = await getConnectionDetails(db, platformConnectionId);
      if (
        !connection ||
        !connection.isActive ||
        !connection.accessTokenEncrypted
      ) {
        /* Log & return */ return;
      }

      // Skip sync for Facebook connections that are pending page selection
      if (
        connection.platform === "facebook" &&
        connection.pendingPageSelection
      ) {
        console.log(
          `Connection ${platformConnectionId} is pending Facebook page selection, skipping sync`
        );
        return;
      }
      const accessToken = await decryptToken(
        connection.accessTokenEncrypted,
        env.ENCRYPTION_KEY
      );
      if (!accessToken) {
        await updateConnectionStatus(db, platformConnectionId, {
          needsReconnect: true,
        });
        return;
      }

      // API Call via Platform Adapter
      const platformAdapter = getPlatformAdapter(connection.platform);
      const syncResult = await platformAdapter.getPosts(connection, {}, env);

      const posts = syncResult.posts || [];

      // Update platform-specific metrics (e.g., YouTube subscriber count)
      // Update platform-specific metrics and last polled date
      const updateData: any = {
        lastPolledAt: new Date(),
        lastSyncedAt: new Date(),
      };

      try {
        await db
          .update(schema.platformConnections)
          .set(updateData)
          .where(eq(schema.platformConnections.id, platformConnectionId));
      } catch (updateError) {
        console.error(
          `Failed to update platform metrics for ${platformConnectionId}:`,
          updateError
        );
      }

      // DB Write via Service
      if (posts && posts.length > 0) {
        console.log(
          `Sync Queue: Upserting ${posts.length} posts for ${platformConnectionId}`
        );
        for (const post of posts) {
          const postData = platformAdapter.mapPostData(post, connection);
          try {
            await upsertPost(db, postData, (post as any).children);
          } catch (d1Error) {
            console.error(
              `Sync Queue: D1 Error upserting post ${postData.mediaId} for conn ${platformConnectionId}:`,
              d1Error
            );
            logErrorToAnalytics(
              env,
              "SYNC_DB_POST_ERROR",
              `Error upserting post ${postData.mediaId} during sync`,
              {
                platformConnectionId,
                mediaId: postData.mediaId,
                error: String(d1Error),
              }
            );
          }
        }
      }
      console.log(
        `Sync Queue: Finished sync successfully for connection ${platformConnectionId}`
      );
    } catch (error: any) {
      console.error(
        `Sync Queue: CRITICAL error processing message for pcId=${platformConnectionId}:`,
        error
      );
      const maxRetries = parseInt(env.QUEUE_MAX_RETRIES || "3", 10); // Ggf. anderes Retry Limit für Sync
      if (message.attempts >= maxRetries) {
        logErrorToAnalytics(
          env,
          "SYNC_FINAL_FAILURE",
          `Sync message failed all retries`,
          {
            platformConnectionId,
            attempts: message.attempts,
            error: String(error),
          }
        );
      } else {
        message.retry({ delaySeconds: 60 * (message.attempts + 1) });
      } // Längeres Backoff für Sync?
    }
  });
  await Promise.allSettled(promises);
}

// --- Scheduled Handler für Token Check ---
async function handleTokenValidityCheck(env: Bindings, ctx: ExecutionContext) {
  console.log(`Scheduled token validity check running...`);
  const db = getDbClient(env.DB);
  const now = Date.now();
  const checkThreshold = now - 23 * 60 * 60 * 1000; // 23 hours ago
  try {
    const connectionsToCheck = await getConnectionsForTokenCheck(
      db,
      checkThreshold
    ); // DB Service
    console.log(
      `Found ${connectionsToCheck.length} connections for validity check.`
    );
    for (const conn of connectionsToCheck) {
      let accessToken: string | null = null;
      if (conn.accessTokenEncrypted)
        accessToken = await decryptToken(
          conn.accessTokenEncrypted,
          env.ENCRYPTION_KEY
        ); // Token Util
      if (!accessToken) {
        await updateConnectionStatus(db, conn.id, { needsReconnect: true });
        logErrorToAnalytics(
          env,
          "TOKEN_CHECK_DECRYPT_FAIL",
          `Failed decrypt token`,
          { platformConnectionId: conn.id }
        );
        continue;
      }

      let isValid = false;
      try {
        try {
          const platformAdapter = getPlatformAdapter(conn.platform);
          isValid = await platformAdapter.checkToken(accessToken, env);

          if (!isValid) {
            await updateConnectionStatus(db, conn.id, { needsReconnect: true });
            logErrorToAnalytics(
              env,
              "TOKEN_CHECK_FAIL",
              `${conn.platform} token invalid`,
              { platformConnectionId: conn.id, platform: conn.platform }
            );
          }
        } catch (adapterError) {
          console.error(
            `Token check failed for platform ${conn.platform}:`,
            adapterError
          );
          logErrorToAnalytics(
            env,
            "TOKEN_CHECK_ADAPTER_ERROR",
            `Platform adapter error for ${conn.platform}`,
            { platformConnectionId: conn.id, error: String(adapterError) }
          );
        }
        if (isValid) {
          await db
            .update(schema.platformConnections)
            .set({ lastCheckedAt: new Date() })
            .where(eq(schema.platformConnections.id, conn.id));
        }
      } catch (fetchError) {
        logErrorToAnalytics(
          env,
          "TOKEN_CHECK_NETWORK_ERROR",
          `Network error during check`,
          { platformConnectionId: conn.id, error: String(fetchError) }
        );
      }
    }
  } catch (error) {
    console.error("CRITICAL Error during scheduled token check:", error);
    logErrorToAnalytics(
      env,
      "SCHEDULED_HANDLER_ERROR",
      `Token check handler failed`,
      { error: String(error) }
    );
  }
}
// Helper für Token Check Fehlerbehandlung
async function handleTokenCheckApiError(
  db: DrizzleD1Database<typeof schema>,
  env: Bindings,
  conn: Pick<PlatformConnection, "id">,
  response: Response
) {
  console.warn(
    `Token Check: API Error Status ${response.status} for conn ${conn.id}`
  );
  if ([400, 401, 403].includes(response.status)) {
    await updateConnectionStatus(db, conn.id, { needsReconnect: true }); // DB Service
    logErrorToAnalytics(
      env,
      "TOKEN_CHECK_AUTH_FAIL",
      `Token invalid or permissions revoked`,
      { platformConnectionId: conn.id, status: response.status }
    );
  } else {
    logErrorToAnalytics(
      env,
      "TOKEN_CHECK_API_ERROR",
      `API error during check, status ${response.status}`,
      { platformConnectionId: conn.id, status: response.status }
    );
  }
}

// --- Scheduled Handler für Basic Display Polling ---
async function handleBasicDisplayPolling(
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Basic Display Polling running...`);
  const db = getDbClient(env.DB);
  try {
    const connectionsToPoll = await getActiveInstagramConnections(db); // DB Service
    console.log(
      `Polling ${connectionsToPoll.length} active Basic Display connections.`
    );
    for (const conn of connectionsToPoll) {
      let accessToken: string | null = null;
      try {
        if (!conn.accessTokenEncrypted) continue;
        accessToken = await decryptToken(
          conn.accessTokenEncrypted,
          env.ENCRYPTION_KEY
        );
        if (!accessToken) {
          await updateConnectionStatus(db, conn.id, { needsReconnect: true });
          continue;
        }

        const platformAdapter = getPlatformAdapter(conn.platform);
        const syncResult = await platformAdapter.getPosts(conn, {}, env);

        const posts = syncResult.posts || [];
        if (posts.length > 0) {
          const existingMediaIds = new Set(
            (
              await db
                .select({ mediaId: schema.posts.mediaId })
                .from(schema.posts)
                .where(eq(schema.posts.platformConnectionId, conn.id))
                .orderBy(desc(schema.posts.timestamp))
                .limit(200)
                .all()
            ).map((p) => p.mediaId)
          );

          let newPostsFound = 0;
          for (const post of posts) {
            const postData = platformAdapter.mapPostData(post, conn);
            if (!existingMediaIds.has(postData.mediaId)) {
              newPostsFound++;
              try {
                await upsertPost(db, postData, (post as any).children);
              } catch (d1Error) {
                logErrorToAnalytics(
                  env,
                  "POLLING_DB_POST_ERROR",
                  `Error upserting polled post ${postData.mediaId}`,
                  {
                    platformConnectionId: conn.id,
                    mediaId: postData.mediaId,
                    error: String(d1Error),
                  }
                );
              }
            }
          }
          if (newPostsFound > 0)
            console.log(
              `Polling: Found ${newPostsFound} new posts for ${conn.id}.`
            );
        }
      } catch (connError: any) {
        if (connError instanceof GraphApiError && connError.isAuthError) {
          await updateConnectionStatus(db, conn.id, { needsReconnect: true });
        }
        console.error(
          `Polling: Unhandled error processing connection ${conn.id}:`,
          connError
        );
        logErrorToAnalytics(
          env,
          "POLLING_CONN_ERROR",
          `Unhandled error polling connection`,
          { platformConnectionId: conn.id, error: String(connError) }
        );
      }
    }
  } catch (error) {
    console.error("CRITICAL Error during Basic Display Polling:", error);
    logErrorToAnalytics(
      env,
      "POLLING_HANDLER_ERROR",
      `Polling handler failed`,
      { error: String(error) }
    );
  }
}

// --- Scheduled Handler für YouTube Polling ---
async function handleYouTubePolling(
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`YouTube Polling running...`);
  const db = getDbClient(env.DB);
  try {
    const connectionsToPoll = await getActiveYouTubeConnections(db);
    console.log(
      `Enqueuing polling for ${connectionsToPoll.length} active YouTube connections.`
    );

    for (const conn of connectionsToPoll) {
      const message: SyncQueueMessage = {
        platformConnectionId: conn.id,
        triggeredByUserId: "youtube_polling",
      };
      await env.SYNC_QUEUE.send(message);
    }
  } catch (error) {
    console.error("CRITICAL Error during YouTube Polling scheduling:", error);
    logErrorToAnalytics(
      env,
      "YOUTUBE_POLLING_HANDLER_ERROR",
      `YouTube polling scheduler failed`,
      { error: String(error) }
    );
  }
}

// --- Scheduled Handler für TikTok Polling ---
async function handleTikTokPolling(
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`TikTok Polling running...`);
  const db = getDbClient(env.DB);
  try {
    const connectionsToPoll = await getActiveTikTokConnections(db);
    console.log(
      `Enqueuing polling for ${connectionsToPoll.length} active TikTok connections.`
    );

    for (const conn of connectionsToPoll) {
      const message: TikTokPollingQueueMessage = {
        platformConnectionId: conn.id,
      };
      await env.TIKTOK_POLLING_QUEUE.send(message);
    }
  } catch (error) {
    console.error("CRITICAL Error during TikTok Polling scheduling:", error);
    logErrorToAnalytics(
      env,
      "TIKTOK_POLLING_HANDLER_ERROR",
      `TikTok polling scheduler failed`,
      { error: String(error) }
    );
  }
}

// --- Scheduled Handler für Facebook Polling ---
async function handleFacebookPolling(
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Facebook Polling running...`);
  const db = getDbClient(env.DB);
  try {
    const connectionsToPoll = await getActiveFacebookConnections(db);
    console.log(
      `Enqueuing polling for ${connectionsToPoll.length} active Facebook connections.`
    );

    for (const conn of connectionsToPoll) {
      const message: SyncQueueMessage = {
        platformConnectionId: conn.id,
        triggeredByUserId: "facebook_polling",
      };
      await env.SYNC_QUEUE.send(message);
    }
  } catch (error) {
    console.error("CRITICAL Error during Facebook Polling scheduling:", error);
    logErrorToAnalytics(
      env,
      "FACEBOOK_POLLING_HANDLER_ERROR",
      `Facebook polling scheduler failed`,
      { error: String(error) }
    );
  }
}

// --- YouTube Subscription Renewal Handler ---
async function handleYouTubeSubscriptionRenewal(
  env: Bindings,
  ctx: ExecutionContext
) {
  console.log(`YouTube subscription renewal running...`);
  const db = getDbClient(env.DB);

  try {
    // Import YouTube PubSub functions
    const { renewYouTubeSubscriptions, getActiveYouTubeChannels } =
      await import("./platforms/youtube-pubsub");

    // Get all active YouTube channels
    const channelIds = await getActiveYouTubeChannels(db);

    if (channelIds.length === 0) {
      console.log("No active YouTube channels found for subscription renewal");
      return;
    }

    console.log(
      `Renewing subscriptions for ${channelIds.length} YouTube channels`
    );

    // Renew subscriptions
    const results = await renewYouTubeSubscriptions(channelIds, env);

    console.log(
      `YouTube subscription renewal completed: ${results.success.length} successful, ${results.failed.length} failed`
    );

    if (results.failed.length > 0) {
      logErrorToAnalytics(
        env,
        "YOUTUBE_SUBSCRIPTION_RENEWAL_PARTIAL_FAILURE",
        "Some YouTube subscriptions failed to renew",
        {
          failedChannels: results.failed,
          successfulChannels: results.success.length,
          totalChannels: channelIds.length,
        }
      );
    }
  } catch (error: any) {
    console.error("YouTube subscription renewal failed:", error);
    logErrorToAnalytics(
      env,
      "YOUTUBE_SUBSCRIPTION_RENEWAL_ERROR",
      "YouTube subscription renewal failed",
      { error: String(error) }
    );
  }
}

// --- Queue Consumer für TikTok Polling (Refactored with Durable Objects) ---
async function processTikTokPollingQueue(
  batch: MessageBatch<TikTokPollingQueueMessage>,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Processing ${batch.messages.length} TikTok polling messages...`);

  const promises = batch.messages.map(async (message) => {
    const { platformConnectionId } = message.body;
    console.log(
      `Processing TikTok polling for connection ${platformConnectionId}`
    );

    // Get Durable Object stubs
    const accountPollingId =
      env.ACCOUNT_POLLING_DO.idFromName(platformConnectionId);
    const accountPollingStub = env.ACCOUNT_POLLING_DO.get(accountPollingId);

    // Use a single global rate limiter DO for this example
    const rateLimiterId = env.RATE_LIMITER_DO.idFromName(
      "global-tiktok-rate-limiter"
    );
    const rateLimiterStub = env.RATE_LIMITER_DO.get(rateLimiterId);

    try {
      // 1. Check rate limit with RateLimiterDO
      const rateLimitCheckResponse =
        await rateLimiterStub.fetch("http://do/check");
      if (!rateLimitCheckResponse.ok) {
        console.log(
          `TikTok Polling: Rate limit active, re-enqueueing ${platformConnectionId}.`
        );
        message.retry({ delaySeconds: 60 }); // Retry after 60 seconds
        return;
      }

      // 2. Try to acquire lock with AccountPollingDO
      const lockResponse = await accountPollingStub.fetch("http://do/lock", {
        method: "POST",
        body: JSON.stringify({ platformConnectionId }),
      });

      if (lockResponse.status === 409) {
        console.log(
          `TikTok Polling: Connection ${platformConnectionId} is already locked, skipping.`
        );
        return; // Another worker is already processing this
      }
      if (!lockResponse.ok) {
        throw new Error(`Failed to acquire lock: ${await lockResponse.text()}`);
      }

      // Function to release the lock
      const releaseLock = async () => {
        await accountPollingStub.fetch("http://do/release", {
          method: "POST",
          body: JSON.stringify({ platformConnectionId }),
        });
      };

      try {
        const db = getDbClient(env.DB);
        // 3. Get connection details
        const connection = await getConnectionDetails(db, platformConnectionId);
        if (
          !connection ||
          !connection.isActive ||
          !connection.accessTokenEncrypted ||
          connection.platform !== "tiktok"
        ) {
          console.warn(
            `TikTok Polling: Conn ${platformConnectionId} inactive/no token/not TikTok.`
          );
          return;
        }

        // 4. Decrypt access token
        const accessToken = await decryptToken(
          connection.accessTokenEncrypted,
          env.ENCRYPTION_KEY
        );
        if (!accessToken) {
          await updateConnectionStatus(db, platformConnectionId, {
            needsReconnect: true,
          });
          return;
        }

        // 5. Fetch TikTok videos using platform adapter
        const adapter = getPlatformAdapter("tiktok");
        const syncResult = await adapter.getPosts(connection as any, {}, env);

        // (Error handling for syncResult is now managed in the catch block)

        const posts = (syncResult as any).posts || [];
        console.log(`Found ${posts.length} posts for ${platformConnectionId}`);
        // ... (rest of the post processing logic remains the same)
      } catch (e: any) {
        if (e instanceof GraphApiError && e.isAuthError) {
          // If we get a rate limit error from the API, trigger the backoff in the DO
          await rateLimiterStub.fetch("http://do/initiateBackoff", {
            method: "POST",
            body: JSON.stringify({ duration: 60000 * 5 }), // 5 minutes backoff
          });
          message.retry({ delaySeconds: 60 * 5 });
        } else {
          console.error(
            `TikTok Polling: Unhandled error processing connection ${platformConnectionId}:`,
            e
          );
          logErrorToAnalytics(
            env,
            "TIKTOK_POLLING_ERROR",
            `Error polling connection`,
            {
              platformConnectionId,
              error: String(e),
            }
          );
        }
      } finally {
        // Always release the lock
        await releaseLock();
      }
    } catch (error: any) {
      console.error(
        `TikTok Polling: Critical error with DO for connection ${platformConnectionId}:`,
        error
      );
      logErrorToAnalytics(
        env,
        "TIKTOK_POLLING_DO_ERROR",
        `Critical error interacting with Durable Object`,
        {
          platformConnectionId,
          error: String(error),
        }
      );
      message.retry({ delaySeconds: 30 }); // Retry on DO failure
    }
  });

  await Promise.allSettled(promises);
}

/**
 * Haupt-Handler für Scheduled Events (Cron Triggers).
 * Leitet die Anfrage basierend auf dem Cron-String an die zuständige Funktion weiter.
 */
async function handleScheduled(
  event: ScheduledEvent,
  env: Bindings,
  ctx: ExecutionContext
) {
  console.log(`Scheduled event triggered by cron: ${event.cron}`);
  // Nutze waitUntil, um sicherzustellen, dass die Verarbeitung Zeit hat, auch wenn der Trigger kurzlebig ist
  if (event.cron === "*/15 * * * *") {
    // Alle 15 Minuten
    ctx.waitUntil(handleBasicDisplayPolling(env, ctx));
    ctx.waitUntil(handleTikTokPolling(env, ctx));
    ctx.waitUntil(handleYouTubePolling(env, ctx));
    ctx.waitUntil(handleFacebookPolling(env, ctx));
  } else if (event.cron === "*/5 * * * *") {
    // Previously every 5 minutes - now unused but kept for reference
    console.log("Skipping unused 5-minute cron trigger");
  } else if (event.cron === "5 3 * * *") {
    // Täglich um 03:05 UTC
    ctx.waitUntil(handleTokenValidityCheck(env, ctx));
  } else if (event.cron === "0 4 * * *") {
    // Alle 6 Stunden
    ctx.waitUntil(handleYouTubeSubscriptionRenewal(env, ctx));
    ctx.waitUntil(refreshTokensHandler(env));
  } else {
    console.warn(`Scheduled event triggered by unexpected cron: ${event.cron}`);
    // Logge diesen unerwarteten Trigger auch
    logErrorToAnalytics(
      env,
      "UNKNOWN_CRON",
      `Unexpected cron trigger received`,
      { cron: event.cron }
    );
  }
}

// --- Exports ---
export { DebouncerDO, AccountPollingDO, RateLimiterDO };
export default {
  fetch: app.fetch, // Hono Request Handler
  queue: async (
    batch: MessageBatch<any>,
    env: Bindings,
    ctx: ExecutionContext
  ) => {
    // Haupt Queue Router
    try {
      // Leite an den richtigen Consumer weiter basierend auf Queue-Namen
      if (batch.queue === "pagination-queue")
        await processPaginationQueue(
          batch as MessageBatch<PaginationQueueMessage>,
          env,
          ctx
        );
      else if (batch.queue === "sync-queue")
        await processSyncQueue(
          batch as MessageBatch<SyncQueueMessage>,
          env,
          ctx
        );
      // Optional: Consumer für DLQs hier hinzufügen, falls gewünscht
      // else if (batch.queue === 'pagination-dlq') await processPaginationDLQ(batch, env, ctx);
      // else if (batch.queue === 'sync-dlq') await processSyncDLQ(batch, env, ctx);
      else {
        console.error(`Msg from unknown queue: ${batch.queue}`);
        logErrorToAnalytics(
          env,
          "UNKNOWN_QUEUE_MESSAGE",
          `Msg from queue ${batch.queue}`,
          { count: batch.messages.length }
        );
        batch.retryAll();
      }
    } catch (e) {
      console.error(`Unhandled queue processor error for ${batch.queue}:`, e);
      logErrorToAnalytics(
        env,
        "QUEUE_PROCESSOR_FATAL",
        `Unhandled error in queue ${batch.queue}`,
        { error: String(e) }
      );
      batch.retryAll();
    }

    // --- Queue Consumer for TikTok Backoff ---
    async function processTikTokBackoffQueue(
      batch: MessageBatch<TikTokBackoffQueueMessage>,
      env: Bindings,
      ctx: ExecutionContext
    ): Promise<void> {
      const promises = batch.messages.map(async (message) => {
        const { platformConnectionId, delayUntil } = message.body;
        if (Date.now() < delayUntil) {
          // Not yet time to process, retry with original delay
          message.retry({
            delaySeconds: Math.ceil((delayUntil - Date.now()) / 1000),
          });
          return;
        }
        // Time to retry, send back to main polling queue
        const pollingMessage: TikTokPollingQueueMessage = {
          platformConnectionId,
          priority: 1, // Medium priority for retries
        };
        await env.TIKTOK_POLLING_QUEUE.send(pollingMessage);
      });
      await Promise.allSettled(promises);
    }
  },
  scheduled: handleScheduled, // Scheduled Event Handler
};

export type AppType = typeof app;
